<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getGrowthRecordStatistics,
  type GrowthRecordStatisticsResponse
} from "@/api/growth-records";

defineOptions({
  name: "GrowthStatistics"
});

const loading = ref(false);
const statisticsData = ref<any>({
  total_users: 0,
  active_users: 0,
  total_plants: 0,
  avg_plant_level: 0,
  total_energy: 0,
  total_meditation_time: 0,
  level_distribution: {},
  plant_level_distribution: {},
  health_status_distribution: {}
});

// 获取统计数据
const fetchStatistics = async () => {
  try {
    loading.value = true;
    const response = await getGrowthRecordStatistics();
    if (response.code === 200) {
      statisticsData.value = response.data;
    } else {
      ElMessage.error(response.message || "获取统计数据失败");
    }
  } catch (error) {
    console.error("获取统计数据失败:", error);
    ElMessage.error("获取统计数据失败");
  } finally {
    loading.value = false;
  }
};

// 格式化时间（分钟转换为小时分钟）
const formatTime = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return hours > 0 ? `${hours}小时${mins}分钟` : `${mins}分钟`;
};

// 获取健康状态文本
const getHealthStatusText = (status: string) => {
  const statusMap = {
    healthy: "健康",
    excellent: "优秀",
    unhealthy: "不健康"
  };
  return statusMap[status] || status;
};

onMounted(() => {
  fetchStatistics();
});
</script>

<template>
  <div class="main">
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-gray-800 mb-2">成长记录统计</h2>
      <p class="text-gray-600">查看用户成长记录的整体统计数据</p>
    </div>

    <div
      v-loading="loading"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
    >
      <!-- 总用户数 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
            <i :class="useRenderIcon('ep:user')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">总用户数</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ statisticsData.total_users }}
            </p>
          </div>
        </div>
      </div>

      <!-- 活跃用户数 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
            <i :class="useRenderIcon('ep:user-filled')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">活跃用户数</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ statisticsData.active_users }}
            </p>
          </div>
        </div>
      </div>

      <!-- 总多肉数量 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-orange-100 text-orange-600 mr-4">
            <span class="text-2xl">🌱</span>
          </div>
          <div>
            <p class="text-sm text-gray-600">总多肉数量</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ statisticsData.total_plants }}
            </p>
          </div>
        </div>
      </div>

      <!-- 平均多肉等级 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
            <i :class="useRenderIcon('ep:trophy')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">平均多肉等级</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ statisticsData.avg_plant_level.toFixed(1) }}
            </p>
          </div>
        </div>
      </div>

      <!-- 总能量值 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4">
            <i :class="useRenderIcon('ep:coin')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">总能量值</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ statisticsData.total_energy.toLocaleString() }}
            </p>
          </div>
        </div>
      </div>

      <!-- 总冥想时长 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-indigo-100 text-indigo-600 mr-4">
            <i :class="useRenderIcon('ep:timer')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">总冥想时长</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ formatTime(statisticsData.total_meditation_time) }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 分布统计 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 冥想等级分布 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">冥想等级分布</h3>
        <div class="space-y-3">
          <div
            v-for="(count, level) in statisticsData.level_distribution"
            :key="level"
            class="flex items-center justify-between"
          >
            <span class="text-sm text-gray-600">等级 {{ level }}</span>
            <div class="flex items-center">
              <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                <div
                  class="bg-blue-500 h-2 rounded-full"
                  :style="{
                    width: `${(count / statisticsData.total_users) * 100}%`
                  }"
                />
              </div>
              <span class="text-sm font-medium text-gray-800">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 多肉等级分布 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">多肉等级分布</h3>
        <div class="space-y-3">
          <div
            v-for="(count, level) in statisticsData.plant_level_distribution"
            :key="level"
            class="flex items-center justify-between"
          >
            <span class="text-sm text-gray-600">等级 {{ level }}</span>
            <div class="flex items-center">
              <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                <div
                  class="bg-green-500 h-2 rounded-full"
                  :style="{
                    width: `${(count / statisticsData.total_plants) * 100}%`
                  }"
                />
              </div>
              <span class="text-sm font-medium text-gray-800">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 健康状态分布 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">健康状态分布</h3>
        <div class="space-y-3">
          <div
            v-for="(count, status) in statisticsData.health_status_distribution"
            :key="status"
            class="flex items-center justify-between"
          >
            <span class="text-sm text-gray-600">{{
              getHealthStatusText(status)
            }}</span>
            <div class="flex items-center">
              <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                <div
                  class="bg-orange-500 h-2 rounded-full"
                  :style="{
                    width: `${(count / statisticsData.total_users) * 100}%`
                  }"
                />
              </div>
              <span class="text-sm font-medium text-gray-800">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 刷新按钮 -->
    <div class="mt-8 text-center">
      <el-button
        type="primary"
        :icon="useRenderIcon('ep:refresh')"
        :loading="loading"
        @click="fetchStatistics"
      >
        刷新数据
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
}
</style>
